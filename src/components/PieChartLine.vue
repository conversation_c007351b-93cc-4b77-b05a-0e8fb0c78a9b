<template>
  <div class="container-box">
    <div class="chart-container">
      <!-- <light-beam
        :beam-count="6"
        beam-color="rgba(0, 255, 204, 0.15)"
        :rotation-speed="3000"
      /> -->
      <particle-effect
        :particle-count="6"
        particle-color="rgba(0, 255, 204, 0.5)"
        :animation-speed="3500"
      />
      <div class="chart-box">
        <div class="chart-title">
          <div class="title">应用领域</div>
        </div>
        <div class="chart" ref="chartRef"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount, nextTick } from "vue";
import * as echarts from "echarts";
import ParticleEffect from "./ParticleEffect.vue";
import LightBeam from "./LightBeam.vue";

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});

const chartRef = ref(null);
let chart = null;

function initChart() {
  if (!chartRef.value) {
    console.warn("Pie chart container not found");
    return;
  }
  try {
    chart = echarts.init(chartRef.value);
    updateChart();
    simulateClick();
  } catch (error) {
    console.error("Failed to initialize pie chart:", error);
  }
}
const simulateClick = () => {
  let currentIndex = 0;
  setInterval(() => {
    // 先取消之前的高亮
    chart &&
      chart.dispatchAction({
        type: "downplay",
        seriesIndex: 0,
        dataIndex: currentIndex,
      });

    currentIndex = (currentIndex + 1) % 4;

    // 高亮当前项
    chart &&
      chart.dispatchAction({
        type: "highlight",
        seriesIndex: 0,
        dataIndex: currentIndex,
      });

    // 显示 tooltip
    // chart.dispatchAction({
    //   type: "showTip",
    //   seriesIndex: 0,
    //   dataIndex: currentIndex,
    // });
  }, 2000);
};
function updateChart() {
  if (!chart) {
    console.warn("Pie chart not initialized");
    return;
  }
  const defaultData = [
    { name: "叉车", value: 0 },
    { name: "大巴车", value: 0 },
    { name: "矿车", value: 0 },
    { name: "其他", value: 0 },
  ];

  let chartData = defaultData;
  if (props.data && props.data.length) {
    chartData = props.data.map((item) => {
      return {
        name: item.vehicleTypeLabel,
        value: item.count,
      };
    });
  }

  if (chartData.length === 0) {
    console.warn("No data provided for pie chart");
    const emptyOption = {
      title: {
        text: "暂无数据",
        left: "center",
        top: "middle",
        textStyle: {
          color: "rgba(255, 255, 255, 0.5)",
          fontSize: 16,
        },
      },
    };
    chart.setOption(emptyOption);
    return;
  }

  const option = {
    tooltip: {
      show: false,
      trigger: "item",
      formatter: "{b}: {c} ({d}%)",
      backgroundColor: "rgba(0, 20, 80, 0.9)",
      borderColor: "#00e4ff",
      borderWidth: 1,
      textStyle: {
        color: "#fff",
        fontSize: 10,
      },
    },
    legend: {
      // orient: "vertical",
      // show: false,
      itemWidth: 10,
      itemHeight: 10,
      width: 15,
      left: "10",
      top: "center",
      textStyle: {
        color: "rgba(255, 255, 255, 1)",
        fontSize: 10,
      },
      itemGap: 18,
      icon: "rect",
    },
    series: [
      {
        name: "应用领域",
        type: "pie",
        radius: ["32%", "68%"],
        center: ["60%", "50%"],
        avoidLabelOverlap: false,
        label: {
          show: true,
          // alignTo: "edge",
          formatter: "{b}",
          minMargin: 5,
          edgeDistance: 30,
          lineHeight: 15,
          rich: {
            time: {
              fontSize: 10,
              color: "rgba(255,255,255,0.6)",
            },
          },
          fontSize: "10",
          fontWeight: "bold",
          color: "#a6f9ff",
          textShadowColor: "rgba(0, 228, 255, 0.4)",
          textShadowBlur: 10,
        },

        labelLine: {
          length: 25,
          maxSurfaceAngle: 80,
          lineStyle: { color: "#a6f9ff" },
        },
        // labelLayout: function (params) {
        //   const isLeft = params.labelRect.x < myChart.getWidth() / 2;
        //   const points = params.labelLinePoints;
        //   // Update the end point.
        //   points[2][0] = isLeft
        //     ? params.labelRect.x
        //     : params.labelRect.x + params.labelRect.width;
        //   return {
        //     labelLinePoints: points,
        //   };
        // },
        emphasis: {
          focus: "series",
          blurScope: "coordinateSystem",
          label: {
            show: true,
            // alignTo: "edge",
            formatter: "{b}\n{c}",
            minMargin: 15,
            lineHeight: 15,
            fontSize: "10",
            fontWeight: "bold",
            color: "#a6f9ff",
            textShadowColor: "rgba(0, 228, 255, 0.4)",
            textShadowBlur: 10,
          },
          itemStyle: {
            shadowBlur: 0,
          },
          scaleSize: 11,
        },
        data: chartData,
        color: ["#00F2FF", "#00C6D5", "#0099AC", "#004C63", "#16364C"],
        // animation: true,
        // animationDuration: 2000,
        // animationEasing: "elasticOut",
        // animationDelay: function (idx) {
        //   return idx * 300;
        // },
        // animationDurationUpdate: 1000,
        // animationEasingUpdate: "cubicInOut",
      },
    ],
  };

  try {
    chart.setOption(option, true);
  } catch (error) {
    console.error("Failed to update pie chart:", error);
  }
}

function resizeChart() {
  if (chart) {
    try {
      chart.resize();
    } catch (error) {
      console.error("Failed to resize pie chart:", error);
    }
  }
}

onMounted(async () => {
  await nextTick();
  initChart();
  window.addEventListener("resize", resizeChart);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", resizeChart);

  if (chart) {
    try {
      chart.dispose();
    } catch (error) {
      console.error("Failed to dispose pie chart:", error);
    } finally {
      chart = null;
    }
  }
});

watch(
  () => props.data,
  (newData, oldData) => {
    updateChart();
  },
  {
    deep: true,
    immediate: false,
  }
);
</script>

<style lang="less" scoped>
.chart-container {
  width: 100%;
  min-height: 0; /* 移除固定最小高度，让flex控制 */
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.chart-container::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  transform: translate(-50%, -50%);
  // background: radial-gradient(
  //   circle,
  //   rgba(0, 228, 255, 0.05) 0%,
  //   transparent 70%
  // );
  border-radius: 50%;
  animation: pie-pulse 3s ease-in-out infinite;
  pointer-events: none;
}

.chart-container:hover {
  box-shadow: 0 0 30px rgba(0, 200, 255, 0.6);
  border-color: rgba(0, 228, 255, 0.6);
}

.chart-container:hover::before {
  animation-duration: 2s;
}

@keyframes pie-pulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.1;
  }
}

.chart-box {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  left: 0px;
  margin-left: 1px;
  // border-radius: 8px;
  // background: #00142c;
  border-radius: 3%;
  .chart-title {
    position: absolute;
    left: -1px;
    top: -1px;
    width: 37%;
    padding-top: 7.4%;
    background: url("../assets/title-mark.png") no-repeat center center;
    background-size: 100% 100%;

    .title {
      position: absolute;
      width: 100%;
      height: 100%;
      inset: 0;
      text-align: left;
      display: flex;
      align-items: center;
      padding-left: 10%;
    }
  }
}
.chart {
  width: 100%;
  height: 100%;
  /* min-height: 250px; */
}
</style>
