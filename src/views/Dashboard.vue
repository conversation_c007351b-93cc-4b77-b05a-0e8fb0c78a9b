<template>
  <div class="dashboard">
    <!-- 头部区域 -->
    <header class="header">
      <div class="center-title">
        <main-title>数据可视化监控平台</main-title>
      </div>
    </header>

    <!-- 中间区域 -->
    <main class="content">
      <div class="main-top">
        <div class="main-top-left">
          <!-- <line-chart title="走势A" :data="orderData" />
              <pie-chart title="占比A" :data="businessData" /> -->
          <div class="box-1">
            <SubTitle title="基础指标" />
            <div class="flex basic-data">
              <div class="flex-1">
                <div class="text-center nimbus">
                  <div class="nimbus-bg"></div>
                  <div class="nimbus-content">
                    <div>
                      <counter-number
                        :total="
                          formatData(basicData.totalEnergy, 1000, 'MWh', 'kWh')
                            .num
                        "
                        color="#6AE6FF"
                      />
                    </div>
                    <div class="text">
                      总装车能量({{
                        formatData(basicData.totalEnergy, 1000, "MWh", "kWh")
                          .unit
                      }})
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <div class="text-center nimbus">
                  <div class="nimbus-bg"></div>
                  <div class="nimbus-content">
                    <div>
                      <counter-number
                        :total="
                          formatData(basicData.dsgTimeSum, 1000, 'kh', 'h').num
                        "
                        color="#6AE6FF"
                      />
                    </div>
                    <div class="text">
                      总运行时长({{
                        formatData(basicData.dsgTimeSum, 1000, "kh", "h").unit
                      }})
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="main-top-center">
          <div class="viewport viewport-s">
            <div class="viewport-box">
              <div class="viewport-content">
                <div class="text1">项目总数</div>
                <div class="text2">单位(个)</div>
                <div class="text3">
                  <counter-number
                    :total="basicData.projectCount"
                    :decimalPlaces="0"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="viewport viewport-m">
            <div class="viewport-box">
              <div class="viewport-content">
                <div class="text1">设备总数</div>
                <div class="text2">单位(台)</div>
                <div class="text3">
                  <counter-number
                    :total="basicData.totalDevices"
                    :decimalPlaces="0"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="viewport viewport-s">
            <div class="viewport-box">
              <div class="viewport-content">
                <div class="text1">覆盖国家</div>
                <div class="text2">单位(个)</div>
                <div class="text3">
                  <counter-number
                    :total="basicData.deviceCityCount"
                    :decimalPlaces="0"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="main-top-right">
          <!-- <line-chart title="走势A" :data="orderData" />
              <pie-chart title="占比A" :data="businessData" /> -->
          <div class="box-1">
            <SubTitleRight title="电量指标" />
            <div class="flex basic-data">
              <div class="flex-1">
                <div class="text-center nimbus">
                  <div class="nimbus-bg"></div>
                  <div class="nimbus-content">
                    <div>
                      <counter-number
                        :total="
                          formatCapacityData(
                            basicData.chgCapSum,
                            basicData.dsgCapSum
                          ).chgNum
                        "
                        color="#6AE6FF"
                      />
                    </div>
                    <div class="text">
                      总充电量（{{
                        formatCapacityData(
                          basicData.chgCapSum,
                          basicData.dsgCapSum
                        ).unit
                      }}）
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <div class="text-center nimbus">
                  <div class="nimbus-bg"></div>
                  <div class="nimbus-content">
                    <div>
                      <counter-number
                        :total="
                          formatCapacityData(
                            basicData.chgCapSum,
                            basicData.dsgCapSum
                          ).dsgNum
                        "
                        color="#6AE6FF"
                      />
                    </div>
                    <div class="text">
                      总放电量（{{
                        formatCapacityData(
                          basicData.chgCapSum,
                          basicData.dsgCapSum
                        ).unit
                      }}）
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="main-content">
        <div class="main-side main-left">
          <div class="main-left-box">
            <pie-chart-line :data="businessData" />
            <!-- <pie-chart title="占比A" :data="businessData" /> -->

            <div class="side-bottom">
              <SubTitle title="区域分布" />
              <Rank :data="platformData" />
            </div>
          </div>
        </div>
        <div class="globe-visualization">
          <div class="panel-content">
            <globe-visualization :data="globeData" />
          </div>
        </div>
        <div class="main-side main-right">
          <div class="main-right-box">
            <line-chart title="充放电趋势" :data="orderData" />
            <!-- <pie-chart title="占比A" :data="businessData" /> -->
            <div class="side-bottom">
              <SubTitleRight title="社会贡献" />
              <div class="donation-container">
                <Donation :data="productData" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <!-- 调试面板 -->
    <debug-panel
      :order-data="orderData"
      :product-data="productData"
      :business-data="businessData"
      :platform-data="platformData"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, reactive, computed } from "vue";
import { useRouter, useRoute } from "vue-router";

import CircleProgress from "../components/CircleProgress.vue";
import CounterNumber from "../components/CounterNumber.vue";
import TrendChart from "../components/TrendChart.vue";
import LineChart from "../components/LineChart.vue";
// import PieChart from "../components/PieChart.vue";
import PieChartLine from "../components/PieChartLine.vue";
import GlobeVisualization from "../components/GlobeVisualization.vue";
import DataTable from "../components/DataTable.vue";
import BarChart from "../components/BarChart.vue";
import DebugPanel from "../components/DebugPanel.vue";
import {
  fetchDashboardBasicData,
  fetchDashboardVehicleData,
  fetchDashboardChargingData,
  fetchDashboardRegionData,
  fetchDashboardStatisticsData,
} from "../api/dashboard";
import MainTitle from "../components/MainTitle.vue";
import SubTitle from "../components/SubTitle.vue";
import SubTitleRight from "../components/SubTitleRight.vue";
import Rank from "../components/Rank.vue";
import Donation from "../components/Donation.vue";
import { formatData } from "../common/util.js";
// 数据定义
const salesData = ref([]);
const orderData = ref([]);
const businessData = ref([]);
const globeData = ref([]);
const platformData = ref([]);
const productData = ref([]);
const route = useRoute(),
  router = useRouter();
// 模拟订单总额实时增长
let orderTimer = null;

const basicData = reactive({
  chgCapSum: void 0,
  deviceCityCount: void 0,
  dsgCapSum: void 0,
  dsgTimeSum: void 0,
  projectCount: void 0,
  totalDevices: void 0,
  totalEnergy: void 0,
});
const supplierId = computed(() => {
  return route.params.supplierId || route.query.supplierId;
});
const getDeviceBasicInfo = async () => {
  const res = await fetchDashboardBasicData(supplierId.value);
  if (res && res.code === 0 && res.data) {
    Object.assign(basicData, res.data);
  }
};
const getVehicleTypeDistribution = async () => {
  const res = await fetchDashboardVehicleData(supplierId.value);
  if (res && res.code === 0 && res.data) {
    // 将数据传递给应用领域折线图组件
    businessData.value = res.data;
  }
};

// 获取充放电趋势数据
const getChargingTrendData = async () => {
  const res = await fetchDashboardChargingData(supplierId.value);
  if (res && res.code === 0 && res.data) {
    // 将数据传递给充放电趋势图组件
    orderData.value = res.data.map((item) => {
      return {
        ...item,
        chargeCap: (Number(item.chargeCap) / 1000).toFixed(2),
        dischargeCap: (Number(item.dischargeCap) / 1000).toFixed(2),
      };
    });
  }
};

// 获取社会贡献数据
const getSocialContributionData = async () => {
  const res = await fetchDashboardStatisticsData(supplierId.value);
  if (res && res.code === 0 && res.data) {
    // 将数据传递给社会贡献组件
    productData.value = res.data;
  }
};

const upDateBasicInfo = async () => {
  getDeviceBasicInfo();
  getVehicleTypeDistribution();
  getChargingTrendData();
  getSocialContributionData();
};
let timer = null;
// 获取数据
onMounted(async () => {
  timer = setInterval(upDateBasicInfo, 30 * 60 * 1000); // 每30分钟刷新一次
  try {
    // 并行获取所有数据
    await Promise.all([
      getDeviceBasicInfo(), // 基础统计数据
      getVehicleTypeDistribution(), // 应用领域折线图数据
      getChargingTrendData(), // 充放电趋势数据
      getSocialContributionData(), // 社会贡献数据
    ]);

    console.log("所有数据获取完成:", {
      basicData: basicData,
      businessData: businessData.value,
      orderData: orderData.value,
      platformData: platformData.value,
      productData: productData.value,
    });
  } catch (error) {
    console.error("获取数据失败:", error);
  }
});

// 组合容量数据格式化函数
const formatCapacityData = (chgCapSum, dsgCapSum) => {
  // 数据验证：如果不是有效数字或小于0，返回默认值
  if (
    isNaN(chgCapSum) ||
    typeof chgCapSum !== "number" ||
    chgCapSum < 0 ||
    isNaN(dsgCapSum) ||
    typeof dsgCapSum !== "number" ||
    dsgCapSum < 0
  ) {
    return { num: 0, unit: "Ah" };
  }

  // 根据两个值的组合决定单位转换
  if (chgCapSum >= 1000000 && dsgCapSum >= 1000000) {
    // 两个值都大于1,000,000，转换为MAh
    const chgNum = Number((chgCapSum / 1000000).toFixed(2));
    const dsgNum = Number((dsgCapSum / 1000000).toFixed(2));
    return { chgNum, dsgNum, unit: "MAh" };
  } else if (chgCapSum >= 1000 && dsgCapSum >= 1000) {
    // 两个值都大于1,000，转换为kAh
    const chgNum = Number((chgCapSum / 1000).toFixed(2));
    const dsgNum = Number((dsgCapSum / 1000).toFixed(2));
    return { chgNum, dsgNum, unit: "kAh" };
  } else {
    // 否则使用Ah
    const chgNum = Number(chgCapSum.toFixed(2));
    const dsgNum = Number(dsgCapSum.toFixed(2));
    return { chgNum, dsgNum, unit: "Ah" };
  }
};
onBeforeUnmount(() => {
  // 清除定时器
  clearInterval(timer);
});
</script>

<style scoped lang="less">
.dashboard {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  padding: 0 0.78vw; /* 20px at 2560px width */
  box-sizing: border-box;
}

.header {
  display: flex;
  height: 12vh;
  padding-bottom: 3vh; /* 20px at 1040px height */
  justify-content: center;
}
.content {
  height: 88vh;
}
.left-stats,
.right-stats {
  width: 25%;
  display: flex;
  flex-direction: column;
}

.center-title {
  width: 50%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* margin-top: 40px; */
}

.digital-counter {
  width: 80%;
  margin-top: 0.96vh; /* 10px at 1040px height */
  padding: 1.44vh 0.59vw; /* 15px at both dimensions */
  background-color: rgba(0, 20, 80, 0.3);
  border-radius: 0.39vw; /* 10px at 2560px width */
  border: 1px solid rgba(0, 228, 255, 0.3);
  box-shadow: 0 0 0.59vw rgba(0, 228, 255, 0.3); /* 15px at 2560px width */
}

.main-content {
  display: flex;
  // flex: 1; /* 占用剩余空间 */
  // min-height: ; /* 690px at 1040px height */
  height: 66vh;
  position: relative;
  z-index: 20;
  .main-left,
  .main-right {
    width: 25%;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 20;
  }
}
.globe-visualization {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 10;
}

.panel-header {
  width: 100%;
  padding: 10px;
  background-color: rgba(0, 20, 80, 0.3);
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border: 1px solid rgba(0, 228, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 228, 255, 0.3);
}

.panel-content {
  width: 100%;
  // height: var(--height-6xl); /* 76.92vh - 响应式高度 */
  // height: 68vh; /* 76.92vh - 响应式高度 */
  // background-color: rgba(0, 20, 80, 0.3);
  // border-bottom-left-radius: 10px;
  // border-bottom-right-radius: 10px;
  // border: 1px solid rgba(0, 228, 255, 0.3);
  // box-shadow: 0 0 15px rgba(0, 228, 255, 0.3);
  width: 140%;
  height: 68vh;
  position: absolute;
  z-index: 30;
  left: -20%;
  top: 0%;
  background-color: transparent !important;
}

.footer {
  display: flex;
  height: 20%;
}

.data-table,
.bar-chart {
  width: 50%;
}
.basic-data {
  height: 13.4vh;
  padding-top: 1vh;
  column-gap: var(--spacing-lg); /* 1.17vw */
}
.nimbus {
  text-align: center;
  width: 100%;
  height: 12.4vh; /* 6.73vh - 响应式高度 */
  position: relative;

  .nimbus-content {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-bottom: 2vh;
    .text {
      color: #a6f9ff;
      font-size: var(--font-md); /* 响应式字体 */
    }
    :deep(.num) {
      text-shadow: 0 0 var(--spacing-xs) rgba(2, 25, 28, 0.5);
    }
  }
  .nimbus-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: url("../assets/niumbus-r.gif") no-repeat center center;
    // background: url("../assets/single.png") no-repeat center center;
    background-size: 100% 100%;
    // &::after {
    //   display: block;
    //   content: "";
    //   position: absolute;
    //   left: 0;
    //   top: 0;
    //   width: 100%;
    //   height: 100%;
    //   z-index: 1;
    //   background: url("../assets/nimbus1.png") no-repeat center center;
    //   background-size: 100% 100%;
    //   opacity: 0.02;
    //   z-index: 0;
    // }
  }
}
@keyframes rotateRing {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.main-top {
  display: flex;
  width: 100%;
  height: 20vh; /* 210px at 1040px height */
  min-width: 200px;
  position: relative;
  z-index: 22;
  .main-top-left {
    width: 28%;
  }
  .main-top-center {
    width: 44%;
  }
  .main-top-right {
    width: 28%;
  }
}
.main-top-center {
  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 2%;
  .viewport {
    width: 18.86%;
    .viewport-box {
      width: 100%;
      padding-top: 15.7vh;
      position: relative;
      background: url("../assets/view-bg1.png") no-repeat center center;
      background-size: 100% 100%;
      backdrop-filter: blur(1px);
      .viewport-content {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        row-gap: 3%;

        .text1 {
          font-size: var(--font-xl); /* 响应式字体 */
          color: #a6f9ff;
        }
        .text2 {
          font-size: var(--font-sm); /* 响应式字体 */
          color: #a6f9ff;
        }
        .text3 {
          font-size: var(--font-3xl); /* 响应式字体 */
          color: #6ae6ff;
          :deep(.num) {
            font-size: var(--font-3xl);
          }
        }
      }
    }
  }
  .viewport-m {
    width: 28.41%;
    .viewport-box {
      width: 100%;
      padding-top: 19vh;
      position: relative;
      .viewport-content {
        .text3 {
          :deep(.num) {
            font-size: var(--font-4xl); /* 响应式字体 */
          }
        }
      }
    }
  }
}
.main-left-box,
.main-right-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: var(--spacing-v-md);
}
.main-left-box {
  padding-right: 1em;
}
.main-right-box {
  padding-left: 1em;
}
/* 图表组件 - 动态分配空间 */
.main-left-box > :first-child,
.main-right-box > :first-child {
  flex: 1; /* 图表组件权重更高 */
  min-height: 0;
  overflow: hidden;
}

/* 底部组件 - 动态分配空间 */
.main-left-box > .side-bottom,
.main-right-box > .side-bottom {
  flex: 1.2; /* 底部组件权重较低 */
  min-height: 0;
  // overflow: hidden;
}

/* 响应式调整 - 根据屏幕高度动态调整 */
@media (max-height: 900px) {
  .main-left-box > :first-child,
  .main-right-box > :first-child {
    flex: 1.2; /* 在较小屏幕上减少图表权重 */
  }
}

@media (max-height: 700px) {
  .main-left-box > :first-child,
  .main-right-box > :first-child {
    flex: 1; /* 在很小屏幕上平均分配 */
  }
}

// @media (min-height: 1200px) {
//   .main-left-box > :first-child,
//   .main-right-box > :first-child {
//     flex: 2; /* 在大屏幕上给图表更多空间 */
//   }
// }

.side-bottom {
  display: flex;
  flex-direction: column;
  width: 100%;
  // height: 100%; /* 确保占满分配的空间 */

  :deep(.title) {
    margin-bottom: var(--spacing-md);
    flex-shrink: 0; /* 标题不收缩 */
  }

  /* 确保内容组件占满剩余空间 */
  > :last-child {
    flex: 1;
    min-height: 0;
  }
}

.donation-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden; /* 防止内容溢出 */
  padding-left: 3px;
}

/* 响应式优化 */
@media (max-height: 800px) {
  .side-bottom {
    gap: var(--spacing-v-xs); /* 减少间距 */
  }

  .donation-list {
    max-height: calc(100% / 4); /* 在小屏幕上进一步限制高度 */
  }
}

@media (max-height: 600px) {
  .main-content {
    min-height: 50vh;
  }

  .donation-container {
    max-height: 30vh; /* 严格限制donation容器高度 */
  }
}
</style>
